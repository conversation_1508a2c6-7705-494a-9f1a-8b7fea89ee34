{"manifest_version": 3, "name": "Clipboard Companion", "version": "1.0.0", "description": "Capture, organize, and paste clipboard items with a visual, draggable interface.", "permissions": ["storage", "clipboardRead", "activeTab", "scripting", "commands"], "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup.html", "default_title": "Clipboard Companion Settings", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "content_scripts": [], "commands": {"toggle-overlay": {"suggested_key": {"default": "Ctrl+Shift+X", "mac": "Command+Shift+X"}, "description": "Toggle clipboard overlay"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["overlay.css", "overlay.js"], "matches": ["<all_urls>"]}]}