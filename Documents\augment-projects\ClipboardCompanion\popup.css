/**
 * Clipboard Companion - Popup Settings Styles
 * Clean, modern interface inspired by sticky notes and notepads
 */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #2d3748;
    background: #f8f9fa;
    width: 380px;
    min-height: 500px;
}

/* Container */
.container {
    padding: 24px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin: 8px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 32px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.header h1 {
    font-size: 20px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 4px;
}

.subtitle {
    font-size: 13px;
    color: #718096;
    font-weight: 400;
}

/* Settings Form */
.settings-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-label {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.label-text {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
}

.label-description {
    font-size: 12px;
    color: #718096;
    font-weight: 400;
}

/* Input Styles */
.input-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0 12px;
    transition: all 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.number-input {
    background: transparent;
    border: none;
    outline: none;
    padding: 12px 0;
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    width: 100%;
}

.input-suffix {
    font-size: 12px;
    color: #718096;
    font-weight: 400;
}

/* Keyboard Shortcut Display */
.shortcut-display {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 12px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.key {
    background: #ffffff;
    border: 1px solid #cbd5e0;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    color: #4a5568;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.plus {
    color: #718096;
    font-size: 12px;
    font-weight: 400;
}

.shortcut-note {
    font-size: 11px;
    color: #718096;
    font-style: italic;
    margin-top: 4px;
}

/* Statistics */
.stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    padding: 16px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 11px;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Buttons */
.button-group {
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

.btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn-primary {
    background: #4299e1;
    color: #ffffff;
}

.btn-primary:hover {
    background: #3182ce;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(66, 153, 225, 0.3);
}

.btn-secondary {
    background: #ffffff;
    color: #718096;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #f7fafc;
    color: #4a5568;
    border-color: #cbd5e0;
}

.btn:active {
    transform: translateY(0);
}

/* Status Messages */
.status-message {
    padding: 12px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    margin-top: 16px;
    text-align: center;
    transition: all 0.3s ease;
}

.status-message.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.status-message.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}

.status-message.hidden {
    display: none;
}

/* Footer */
.footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.footer p {
    font-size: 11px;
    color: #718096;
}

.footer kbd {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 10px;
    color: #4a5568;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.container {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .container {
        margin: 4px;
        padding: 20px;
    }
    
    body {
        width: 100%;
        min-width: 320px;
    }
}
