<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClipPie Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-area {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .input-field {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <h1>🥧 ClipPie Test Page</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Copy some text (Ctrl+C)</li>
            <li>Press <strong>Ctrl+Shift+X</strong> to open ClipPie overlay</li>
            <li>Try clicking tags to paste</li>
            <li>Try dragging tags to the input fields below</li>
        </ol>
    </div>

    <div class="test-area">
        <h3>Test Input Fields</h3>
        
        <label for="text-input">Text Input:</label>
        <input type="text" id="text-input" class="input-field" placeholder="Click here and try pasting or drag tags here">
        
        <label for="email-input">Email Input:</label>
        <input type="email" id="email-input" class="input-field" placeholder="<EMAIL>">
        
        <label for="textarea">Textarea:</label>
        <textarea id="textarea" class="input-field" rows="4" placeholder="Multi-line text area for testing"></textarea>
        
        <label for="contenteditable">Content Editable Div:</label>
        <div id="contenteditable" class="input-field" contenteditable="true" style="min-height: 60px; background: #fafafa;">
            Click here to edit this content editable div
        </div>
    </div>

    <div class="test-area">
        <h3>Test Text for Copying</h3>
        <p>Select and copy this text: <strong>Hello from ClipPie test!</strong></p>
        <p>Or this one: <em>Testing drag and drop functionality</em></p>
        <p>Another test: <code>console.log("ClipPie is working!");</code></p>
    </div>

    <script>
        // Add some debugging
        console.log('ClipPie test page loaded');
        
        // Track focus events
        document.addEventListener('focusin', (e) => {
            console.log('Focus in:', e.target.tagName, e.target.type, e.target.id);
        });
        
        document.addEventListener('focusout', (e) => {
            console.log('Focus out:', e.target.tagName, e.target.type, e.target.id);
        });
        
        // Track paste events
        document.addEventListener('paste', (e) => {
            console.log('Paste event detected on:', e.target.tagName, e.target.type, e.target.id);
        });
    </script>
</body>
</html>
