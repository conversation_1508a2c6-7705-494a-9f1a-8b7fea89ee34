# Contributing to <PERSON><PERSON><PERSON><PERSON>

Thank you for your interest in contributing to <PERSON><PERSON><PERSON><PERSON>! This document provides guidelines for contributing to the project.

## Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/your-username/ClipPie.git
   cd ClipPie
   ```
3. **Load the extension** in Chrome for testing:
   - Open `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the project directory

## Development Guidelines

### Code Style

- Use **ES6+** JavaScript features
- Follow **consistent indentation** (2 spaces)
- Use **meaningful variable names**
- Add **comments** for complex logic
- Keep functions **small and focused**

### File Structure

```
ClipPie/
├── manifest.json          # Extension configuration
├── background.js          # Service worker
├── content.js            # Content script for overlay
├── popup.html/css/js     # Settings interface
├── overlay.css           # Overlay styling
├── icons/                # Extension icons
└── docs/                 # Documentation
```

### Testing

Before submitting changes:

1. **Test basic functionality:**
   - Copy text in Chrome
   - Open overlay with Ctrl+Shift+X
   - Verify tag cloud displays correctly
   - Test click-to-paste functionality

2. **Test edge cases:**
   - Very long text content
   - Special characters and emojis
   - Multiple rapid copy operations
   - Different websites and input fields

3. **Test settings:**
   - Change history limit
   - Clear history
   - Verify statistics update

### Submitting Changes

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** with clear, focused commits:
   ```bash
   git commit -m "Add: Brief description of what you added"
   git commit -m "Fix: Brief description of what you fixed"
   ```

3. **Test thoroughly** on different websites and scenarios

4. **Update documentation** if needed

5. **Submit a pull request** with:
   - Clear description of changes
   - Screenshots/GIFs if UI changes
   - Test cases covered
   - Any breaking changes noted

## Types of Contributions

### 🐛 Bug Reports

When reporting bugs, please include:
- Chrome version
- Operating system
- Steps to reproduce
- Expected vs actual behavior
- Console errors (if any)

### ✨ Feature Requests

For new features, please:
- Check existing issues first
- Describe the use case
- Explain why it would be valuable
- Consider implementation complexity

### 🔧 Code Contributions

We welcome:
- Bug fixes
- Performance improvements
- UI/UX enhancements
- New features (discuss first in issues)
- Documentation improvements

### 📚 Documentation

Help improve:
- README clarity
- Code comments
- Installation instructions
- Usage examples
- API documentation

## Development Tips

### Chrome Extension Development

- Use **Chrome DevTools** for debugging
- Check **Console** for errors in background script
- Use **Inspect** on extension popup for popup debugging
- Monitor **chrome://extensions/** for error messages

### Common Issues

1. **Clipboard API limitations:**
   - Only works when extension has focus
   - Text-only support (no images)
   - Security restrictions on some sites

2. **Content Script injection:**
   - May fail on chrome:// pages
   - CSP restrictions on some sites
   - Timing issues with dynamic content

3. **Storage limitations:**
   - Chrome storage quotas
   - Sync vs local storage considerations

## Code Review Process

1. **Automated checks** will run on your PR
2. **Manual review** by maintainers
3. **Testing** on different environments
4. **Feedback** and requested changes
5. **Approval** and merge

## Community Guidelines

- Be **respectful** and **constructive**
- **Help others** learn and contribute
- **Follow** the code of conduct
- **Ask questions** if unsure about anything

## Getting Help

- **GitHub Issues:** For bugs and feature requests
- **Discussions:** For questions and general discussion
- **Email:** <EMAIL> for private matters

## Recognition

Contributors will be:
- Listed in the README
- Credited in release notes
- Invited to be maintainers (for significant contributions)

Thank you for contributing to ClipPie! 🎉
