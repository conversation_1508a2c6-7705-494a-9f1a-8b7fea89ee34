/**
 * Clipboard Companion - Background Service Worker
 * Handles clipboard monitoring, storage management, and command listeners
 */

// Default settings
const DEFAULT_SETTINGS = {
  historyLimit: 25,
  shortcut: 'Ctrl+Shift+X'
};

// Initialize extension on install
chrome.runtime.onInstalled.addListener(async () => {
  console.log('Clipboard Companion installed');
  
  // Set default settings if not already set
  const settings = await chrome.storage.local.get(['settings']);
  if (!settings.settings) {
    await chrome.storage.local.set({ 
      settings: DEFAULT_SETTINGS,
      clipboardHistory: []
    });
  }
});

// Listen for keyboard command to toggle overlay
chrome.commands.onCommand.addListener(async (command) => {
  if (command === 'toggle-overlay') {
    try {
      // Get the active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (tab && tab.id) {
        // Check if content script is already injected
        try {
          await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
          // If we get here, content script exists, just toggle overlay
          await chrome.tabs.sendMessage(tab.id, { action: 'toggleOverlay' });
        } catch (error) {
          // Content script not injected, inject it first
          await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
          });
          
          // Wait a bit for injection to complete, then show overlay
          setTimeout(async () => {
            try {
              await chrome.tabs.sendMessage(tab.id, { action: 'showOverlay' });
            } catch (e) {
              console.error('Failed to show overlay after injection:', e);
            }
          }, 100);
        }
      }
    } catch (error) {
      console.error('Error handling toggle command:', error);
    }
  }
});

// Clipboard monitoring function
async function monitorClipboard() {
  try {
    // Read current clipboard content
    const clipboardText = await navigator.clipboard.readText();
    
    if (clipboardText && clipboardText.trim()) {
      await addToClipboardHistory(clipboardText.trim());
    }
  } catch (error) {
    // Clipboard read failed - this is normal when extension doesn't have focus
    // We'll only log actual errors, not permission denials
    if (error.name !== 'NotAllowedError') {
      console.error('Clipboard monitoring error:', error);
    }
  }
}

// Add item to clipboard history with FIFO management
async function addToClipboardHistory(text) {
  try {
    const { clipboardHistory = [], settings = DEFAULT_SETTINGS } = await chrome.storage.local.get(['clipboardHistory', 'settings']);
    
    // Don't add if it's the same as the last item
    if (clipboardHistory.length > 0 && clipboardHistory[0].content === text) {
      return;
    }
    
    // Create new clipboard item
    const newItem = {
      id: Date.now().toString(),
      content: text,
      timestamp: Date.now(),
      type: 'text' // For future expansion to support images/links
    };
    
    // Add to beginning of array (most recent first)
    const updatedHistory = [newItem, ...clipboardHistory];
    
    // Enforce history limit (FIFO - remove oldest items)
    const limitedHistory = updatedHistory.slice(0, settings.historyLimit || DEFAULT_SETTINGS.historyLimit);
    
    // Save to storage
    await chrome.storage.local.set({ clipboardHistory: limitedHistory });
    
    console.log('Added to clipboard history:', text.substring(0, 50) + '...');
  } catch (error) {
    console.error('Error adding to clipboard history:', error);
  }
}

// Start clipboard monitoring when extension starts
// Note: Due to security restrictions, clipboard reading only works when extension has focus
// We'll monitor periodically and handle permission errors gracefully
setInterval(monitorClipboard, 1000);

// Listen for messages from content scripts or popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'getClipboardHistory') {
    chrome.storage.local.get(['clipboardHistory']).then(result => {
      sendResponse({ history: result.clipboardHistory || [] });
    });
    return true; // Keep message channel open for async response
  }
  
  if (message.action === 'clearHistory') {
    chrome.storage.local.set({ clipboardHistory: [] }).then(() => {
      sendResponse({ success: true });
    });
    return true;
  }
  
  if (message.action === 'updateSettings') {
    chrome.storage.local.set({ settings: message.settings }).then(() => {
      sendResponse({ success: true });
    });
    return true;
  }
});

// Handle clipboard write requests from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'writeToClipboard') {
    navigator.clipboard.writeText(message.text).then(() => {
      sendResponse({ success: true });
    }).catch(error => {
      console.error('Failed to write to clipboard:', error);
      sendResponse({ success: false, error: error.message });
    });
    return true;
  }
});
