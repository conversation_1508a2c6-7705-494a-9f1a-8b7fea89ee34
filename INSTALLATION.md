# Installation Guide - Clipboard Companion

## Prerequisites

- Google Chrome 88+ or Chromium-based browser
- Developer mode enabled in Chrome extensions

## Step-by-Step Installation

### 1. Prepare the Extension

1. **Download/Clone** this repository to your local machine
2. **Create Icon Files** (Required):
   - Convert the provided `icons/icon.svg` to PNG format in these sizes:
     - `icon16.png` (16x16 pixels)
     - `icon32.png` (32x32 pixels) 
     - `icon48.png` (48x48 pixels)
     - `icon128.png` (128x128 pixels)
   - Place all PNG files in the `icons/` directory
   - You can use online SVG to PNG converters or tools like Inkscape

### 2. Load Extension in Chrome

1. **Open Chrome** and navigate to `chrome://extensions/`
2. **Enable Developer Mode** by toggling the switch in the top-right corner
3. **Click "Load unpacked"** button
4. **Select the extension directory** (the folder containing `manifest.json`)
5. **Confirm** the extension loads without errors

### 3. Verify Installation

1. **Check Extension Icon** appears in Chrome toolbar
2. **Click the icon** to open settings popup
3. **Test Keyboard Shortcut** (Ctrl+Shift+X) on any webpage
4. **Copy some text** and verify it appears in the overlay

### 4. Configure Settings

1. **Click extension icon** to open settings
2. **Set history limit** (default: 25 items)
3. **Note the keyboard shortcut** (Ctrl+Shift+X)
4. **Save settings** and test functionality

## Troubleshooting

### Extension Won't Load
- **Check manifest.json** for syntax errors
- **Ensure all files** are present in the directory
- **Verify icon files** exist (PNG format required)
- **Check Chrome console** for error messages

### Clipboard Not Working
- **Grant permissions** when prompted
- **Try copying text** while extension popup is open
- **Remember**: Clipboard reading requires extension focus (Chrome security)
- **Check if website** has strict Content Security Policy

### Overlay Not Appearing
- **Verify keyboard shortcut** (Ctrl+Shift+X)
- **Try clicking extension icon** first, then use shortcut
- **Check if content script** injected properly
- **Refresh the webpage** and try again

### Icons Not Displaying
- **Convert SVG to PNG** files in required sizes
- **Place PNG files** in `icons/` directory with exact names:
  - `icon16.png`, `icon32.png`, `icon48.png`, `icon128.png`
- **Reload extension** after adding icons

## Permissions Explained

The extension requests these permissions:

- **storage**: Store clipboard history locally
- **clipboardRead**: Read clipboard content when copying
- **activeTab**: Access current tab for content script injection
- **scripting**: Inject overlay content script
- **commands**: Handle keyboard shortcuts

## Development Mode vs Production

### Development Mode (Current)
- Load unpacked extension
- Files can be edited and reloaded
- Console logging enabled
- No Chrome Web Store restrictions

### Production Deployment
- Package as .crx file
- Submit to Chrome Web Store
- Code review and approval process
- Automatic updates for users

## Next Steps

1. **Test thoroughly** on different websites
2. **Copy various types** of text content
3. **Try drag-and-drop** functionality
4. **Adjust settings** as needed
5. **Report any issues** or suggestions

## Uninstallation

1. Go to `chrome://extensions/`
2. Find "Clipboard Companion"
3. Click "Remove" button
4. Confirm removal

All stored clipboard data will be deleted automatically.
